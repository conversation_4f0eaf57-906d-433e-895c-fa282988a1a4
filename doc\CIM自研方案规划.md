# CIM/MES/RTM 自研方案规划

## 第二阶段：详细设计与开发

本阶段的目标是将项目蓝图转化为具体的、可执行的开发施工图。方案将严格遵循《封装全流程系统构建专家-JD》中定义的技术要求和分阶段实施目标。

### 1. 总体技术架构 (Overall Technical Architecture)

为兼顾业务流程的灵活性与设备通信的高性能、高可用要求，我们采用业界成熟的**混合式微服务架构**。

![Architecture Diagram](https://i.imgur.com/8v7xU3g.png)

*   **业务应用层 (MES on Odoo):**
    *   **技术栈:** Odoo 18.0 (Python)。
    *   **职责:** 作为系统的业务流程引擎。负责工单管理、WIP追踪、物料管理、配方逻辑管理、SPC规则配置、以及与SAP等外部系统的集成。充分利用Odoo强大的ERP基础和灵活的业务模型定制能力。

*   **实时服务层 (CIM/EAP & RTM Services):**
    *   **技术栈:** Java (Spring Boot) 为主，Python/TypeScript (Node.js) 为辅。
        *   **Java:** 用于构建CIM/EAP核心服务。Java在企业级应用中的稳定性、丰富的库生态（如SECS/GEM协议库）以及成熟的多线程处理能力，是保障500+设备7x24小时稳定运行的基石。
        *   **Python/TS:** 用于开发辅助性的数据处理、AI算法集成或特定协议的快速原型验证。
    *   **职责:** 这是系统的实时数据处理核心。负责与产线设备（SECS/GEM, Modbus等）进行高速、稳定的双向通信，执行EAP层的防错漏等业务逻辑，并为RTM系统提供实时数据流。

*   **集成与通信层 (Integration Layer):**
    *   **技术栈:** RabbitMQ (消息队列) + gRPC (远程过程调用)。
    *   **职责:**
        *   **RabbitMQ:** 负责MES与CIM/EAP服务之间的**异步消息**传递，实现系统解耦。例如，MES下发生产指令，EAP上报设备状态和采集数据，都通过消息队列进行，避免了直接调用带来的阻塞和性能瓶颈。
        *   **gRPC:** 用于需要快速响应的**同步请求**场景，例如前端请求实时获取某个设备的参数。

*   **数据存储层 (Data Persistence Layer):**
    *   **技术栈:** SQL Server + InfluxDB (时序数据库)。
    *   **职责:**
        *   **SQL Server:** 作为MES的主数据库（遵循JD要求），存储所有业务核心数据，如工单、产品、批次历史、设备主数据、配方参数等结构化数据。
        *   **InfluxDB:** 存储由RTM采集的设备高频时序数据，如传感器读数、温度曲线等。其高效的写入和查询性能是实时监控和后续工艺分析的关键。

### 2. 功能模块化拆解 (Module Breakdown)

#### 2.1 CIM/EAP 实时服务 (Java)

*   **设备驱动模块 (Device Drivers):**
    *   为每种通信协议开发标准驱动，如 `SecsGemDriver`, `ModbusTcpDriver`。
    *   在标准驱动基础上，为核心设备（如长川分选机、三温测试箱）开发专用驱动，封装其特定的消息集和业务场景。
*   **通信核心 (Communication Core):**
    *   负责管理与所有设备的TCP/IP长连接，处理连接的建立、心跳维持和断线重连。
*   **EAP业务逻辑 (EAP Logic):**
    *   实现核心的设备状态机（SECS-II标准）。
    *   开发配方管理模块，负责配方的下载与校验。
    *   开发数据采集计划（DCP）模块，根据MES指令动态调整采集的参数。
    *   **开发防错漏混模块：** 这是CIM的关键价值所在。实现库位、物料、载具的扫码校验，以及ADC（Auto Data Collection）双芯片验证逻辑。
*   **消息网关 (Message Gateway):**
    *   作为EAP服务与RabbitMQ之间的接口，负责消息的序列化、发布与订阅。

#### 2.2 RTM 实时监控服务 (Java/Python)

*   **数据采集器 (Collector):** 订阅RabbitMQ中的设备数据主题，实时接收EAP上报的数据。
*   **数据存储器 (Storage):** 将采集到的数据进行清洗、格式化，高效批量写入InfluxDB。
*   **规则引擎 (Rule Engine):**
    *   实现SPC（统计过程控制）核心规则（如尼尔森八大判异准则）。
    *   从Odoo MES获取控制图（Control Chart）的配置参数（UCL, LCL, Center Line）。
    *   实时判断数据点是否触发异常，并通过RabbitMQ向上游发送告警消息。

#### 2.3 MES 业务应用 (Odoo Modules)

*   **`cim_factory_model` (工厂建模):**
    *   扩展Odoo现有模型，定义产线（Line）、工站（Station）、设备（Equipment）的层级关系和属性。
*   **`cim_wip_management` (WIP管理):**
    *   核心模块。创建 `cim.lot` (生产批次) 和 `cim.wafer` (晶圆) 模型。
    *   实现`Track-In`, `Track-Out`操作界面，并与EAP服务联动。
    *   记录每个Lot/Wafer的全流程历史（`cim.lot.history`）。
*   **`cim_recipe_management` (配方管理):**
    *   创建 `cim.recipe` 模型，管理不同产品、不同工序的设备加工程序。
    *   提供配方的创建、审批、版本控制和下发功能。
*   **`cim_spc_quality` (SPC质量管理):**
    *   创建`cim.spc.control_chart`模型，用于配置SPC控制图参数。
    *   可视化展示来自RTM的实时数据和报警信息。
*   **`cim_equipment_management` (设备管理):**
    *   扩展Odoo的 `maintenance` 模块，与EAP上报的设备状态联动，实现OEE（设备综合效率）的自动计算。

### 3. 数据库与接口设计 (DB & API Design)

#### 3.1 数据库设计 (关键表)

*   **SQL Server (MES):**
    *   `cim_lot`: 批次主表 (产品ID, 数量, 状态, 当前工站...)
    *   `cim_lot_history`: 批次历史表 (批次ID, 操作类型, 设备, 操作员, 时间戳...)
    *   `cim_recipe_body`: 配方主体 (配方名, 版本, 状态...)
    *   `cim_recipe_parameter`: 配方参数 (配方ID, 参数名, 值...)
*   **InfluxDB (RTM):**
    *   Measurement: `equipment_metrics`
    *   Tags: `equipment_id`, `parameter_name`, `lot_id`
    *   Fields: `value_float`, `value_string`

#### 3.2 核心接口设计 (Asynchronous via RabbitMQ)

*   **MES -> EAP (指令通道):**
    *   **`ProcessLot`**: `{ "lot_id": "LOT20240803", "equipment_id": "TESTER-01", "recipe_name": "RECIPE_A.v2" }`
    *   **`ChangeEqpState`**: `{ "equipment_id": "SORTER-05", "target_state": "RUN" }`
*   **EAP -> MES (数据/事件通道):**
    *   **`LotProcessCompleted`**: `{ "lot_id": "LOT20240803", "equipment_id": "TESTER-01", "result": "PASS", "data": [...] }`
    *   **`EqpStateChanged`**: `{ "equipment_id": "SORTER-05", "current_state": "IDLE" }`
    *   **`AlarmOccurred`**: `{ "equipment_id": "BONDER-02", "alarm_code": "E-1024", "alarm_text": "Bonding head pressure low" }`

### 4. 开发路线图与迭代计划 (Roadmap & Sprints)

我们将严格按照JD中定义的分阶段目标进行迭代开发。

*   **阶段一: 试点期 (预计3个月) - "完成1条FT产线设备联网与数据采集"**
    *   **目标:** 跑通最小闭环，验证核心架构。
    *   **Sprint 1-2 (月1):** 架构搭建。完成Odoo基础模块、Java EAP服务骨架、RabbitMQ和数据库的部署。
    *   **Sprint 3-4 (月2):** 设备驱动开发。集中力量开发**长川平移分选机**的SECS/GEM驱动，并完成EAP与设备的稳定通讯。
    *   **Sprint 5-6 (月3):** 端到端联调。开发MES中最基础的WIP Track-In/Out功能，实现MES下发指令 -> EAP控制设备 -> EAP采集数据 -> MES接收并展示数据 的完整流程。
    *   **验收标准:** FT产线至少1台分选机接入成功率≥99%，设备状态和关键参数能被MES实时记录。

*   **阶段二: 推广期 (预计6个月) - "实现CP→FT全流程设备协同"**
    *   **目标:** 横向扩展，覆盖核心流程。
    *   **Sprint 7-10 (月4-5):** 全面驱动开发。并行开发焊线机、测试机、三温测试箱等其他关键设备的驱动。
    *   **Sprint 11-14 (月6-7):** MES功能完善。开发配方管理、SPC质量管理、设备管理等核心业务模块。
    *   **Sprint 15-18 (月8-9):** EAP高级逻辑。开发并上线跨设备协同、防错漏混（库位校验、ADC）等高级功能。
    *   **验收标准:** CP到FT的核心设备全部联网，系统管理全流程生产，指令冲突率≤0.1%。

*   **阶段三: 优化与赋能期 (持续进行)**
    *   **目标:** 数据驱动，智能赋能。
    *   **后续Sprint:**
        *   开发丰富的RTM实时监控看板和OEE报表。
        *   基于收集的历史数据，与AI团队合作，进行良率预测、异常根因分析等算法模型的开发与集成。
        *   根据产线反馈，持续优化系统，提升用户体验和系统稳定性。

---
本方案为第二阶段的详细规划，后续将根据项目进展持续更新。
