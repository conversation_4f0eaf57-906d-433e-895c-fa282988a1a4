version: '3.8'

services:
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: mssql_server_cim
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=yourStrong(!)Password123
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql

  influxdb:
    image: influxdb:2.7
    container_name: influxdb_cim
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=adminadmin
      - DOCKER_INFLUXDB_INIT_ORG=jscim
      - DOCKER_INFLUXDB_INIT_BUCKET=rtm_data

  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: rabbitmq_cim
    ports:
      - "5672:5672"   # AMQP port for services
      - "15672:15672" # Management UI
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  sqlserver_data:
  influxdb_data:
  rabbitmq_data:
