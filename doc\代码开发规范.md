# 代码开发规范与标准

## 文档概述

### 目的和范围
本文档规定了MES/MDM项目的代码开发标准，确保代码质量、可维护性和团队协作效率。

### 适用技术栈
- **后端**: Java 17+ / Spring Boot 3.x / Spring Cloud
- **前端**: Vue 3 / TypeScript / Element Plus
- **数据库**: PostgreSQL / Redis / TimescaleDB
- **构建工具**: Maven / Vite
- **容器化**: Docker / Kubernetes

---

## 第一部分：Java后端开发规范

### 1.1 项目结构规范

#### 1.1.1 Maven项目结构
```
mes-mdm-platform/
├── pom.xml                           # 父项目POM
├── README.md                         # 项目说明
├── docker-compose.yml               # 本地开发环境
├── docs/                            # 项目文档
├── scripts/                         # 构建和部署脚本
├── mes-common/                      # 公共模块
│   ├── mes-common-core/             # 核心工具类
│   ├── mes-common-security/         # 安全组件
│   ├── mes-common-web/              # Web组件
│   └── mes-common-data/             # 数据访问组件
├── mes-gateway/                     # API网关
├── mes-auth/                        # 认证服务
├── mes-mdm/                         # MDM服务
│   ├── mes-mdm-api/                 # 对外接口
│   ├── mes-mdm-core/                # 核心业务
│   └── mes-mdm-infrastructure/      # 基础设施
├── mes-production/                  # 生产管理服务
├── mes-quality/                     # 质量管理服务
└── mes-monitor/                     # 监控服务
```

#### 1.1.2 服务模块结构
```
mes-mdm/
├── mes-mdm-api/                     # API接口层
│   ├── src/main/java/
│   │   └── com/company/mes/mdm/api/
│   │       ├── controller/          # REST控制器
│   │       ├── dto/                 # 数据传输对象
│   │       └── facade/              # 门面服务
│   └── src/main/resources/
├── mes-mdm-core/                    # 业务核心层
│   ├── src/main/java/
│   │   └── com/company/mes/mdm/core/
│   │       ├── domain/              # 领域模型
│   │       │   ├── entity/          # 实体对象
│   │       │   ├── valueobject/     # 值对象
│   │       │   └── service/         # 领域服务
│   │       ├── application/         # 应用服务
│   │       │   ├── service/         # 应用服务
│   │       │   ├── command/         # 命令对象
│   │       │   └── query/           # 查询对象
│   │       └── port/                # 端口接口
│   │           ├── inbound/         # 入站端口
│   │           └── outbound/        # 出站端口
└── mes-mdm-infrastructure/          # 基础设施层
    ├── src/main/java/
    │   └── com/company/mes/mdm/infrastructure/
    │       ├── persistence/         # 数据持久化
    │       │   ├── repository/      # 仓库实现
    │       │   ├── mapper/          # MyBatis映射
    │       │   └── entity/          # 持久化实体
    │       ├── messaging/           # 消息处理
    │       ├── external/            # 外部系统适配
    │       └── config/              # 配置类
    └── src/main/resources/
        ├── mapper/                  # MyBatis映射文件
        ├── db/migration/            # 数据库脚本
        └── application.yml          # 配置文件
```

### 1.2 命名规范

#### 1.2.1 包命名规范
```java
// 基础包结构
com.company.mes                      # 项目根包
├── .common                          # 公共组件
├── .gateway                         # 网关服务
├── .auth                           # 认证服务
├── .mdm                            # MDM服务
└── .production                     # 生产服务

// 详细包结构示例
com.company.mes.mdm.api.controller   # 控制器
com.company.mes.mdm.core.domain     # 领域模型
com.company.mes.mdm.infrastructure  # 基础设施

// 包命名规则:
// 1. 全部小写
// 2. 使用点号分隔
// 3. 避免使用下划线或连字符
// 4. 不使用Java关键字
```

#### 1.2.2 类命名规范
```java
// 实体类 - 使用名词，PascalCase
public class WorkOrder { }              // 工单
public class ProductionLine { }         // 产线
public class QualityInspection { }      // 质量检验

// 服务类 - 使用Service后缀
public class WorkOrderService { }       // 业务服务
public class WorkOrderDomainService { } // 领域服务
public class WorkOrderApplicationService { } // 应用服务

// 控制器类 - 使用Controller后缀
public class WorkOrderController { }    // REST控制器

// 仓库类 - 使用Repository后缀
public class WorkOrderRepository { }    // 仓库接口
public class WorkOrderRepositoryImpl { } // 仓库实现

// DTO类 - 使用DTO后缀或相关后缀
public class WorkOrderDTO { }           // 数据传输对象
public class WorkOrderRequest { }       // 请求对象
public class WorkOrderResponse { }      // 响应对象
public class WorkOrderQuery { }         // 查询对象
public class WorkOrderCommand { }       // 命令对象

// 工具类 - 使用Utils后缀
public class DateUtils { }              // 日期工具
public class StringUtils { }            // 字符串工具

// 常量类 - 使用Constants后缀
public class SystemConstants { }        // 系统常量
public class ErrorConstants { }         // 错误常量

// 配置类 - 使用Config后缀
public class DatabaseConfig { }         // 数据库配置
public class SecurityConfig { }         // 安全配置
```

#### 1.2.3 方法命名规范
```java
// 方法命名使用camelCase，动词开头
public class WorkOrderService {
    
    // 查询方法 - get/find/query开头
    public WorkOrder getWorkOrderById(Long id) { }
    public List<WorkOrder> findWorkOrdersByStatus(String status) { }
    public Page<WorkOrder> queryWorkOrders(WorkOrderQuery query) { }
    
    // 创建方法 - create/add/save开头
    public WorkOrder createWorkOrder(WorkOrderRequest request) { }
    public void addWorkOrderItem(Long workOrderId, WorkOrderItem item) { }
    public WorkOrder saveWorkOrder(WorkOrder workOrder) { }
    
    // 更新方法 - update/modify开头
    public WorkOrder updateWorkOrder(Long id, WorkOrderRequest request) { }
    public void modifyWorkOrderStatus(Long id, String status) { }
    
    // 删除方法 - delete/remove开头
    public void deleteWorkOrder(Long id) { }
    public void removeWorkOrderItem(Long workOrderId, Long itemId) { }
    
    // 验证方法 - validate/check/verify开头
    public void validateWorkOrder(WorkOrder workOrder) { }
    public boolean checkWorkOrderExists(Long id) { }
    public boolean verifyWorkOrderPermission(Long id, String userId) { }
    
    // 计算方法 - calculate/compute开头
    public BigDecimal calculateWorkOrderCost(Long id) { }
    public int computeWorkOrderProgress(Long id) { }
    
    // 转换方法 - convert/transform/to开头
    public WorkOrderDTO convertToDTO(WorkOrder workOrder) { }
    public WorkOrder transformFromRequest(WorkOrderRequest request) { }
    public String toJsonString(WorkOrder workOrder) { }
    
    // 布尔方法 - is/has/can/should开头
    public boolean isWorkOrderCompleted(Long id) { }
    public boolean hasWorkOrderPermission(Long id, String userId) { }
    public boolean canStartWorkOrder(Long id) { }
    public boolean shouldNotifyUser(Long workOrderId) { }
}
```

#### 1.2.4 变量命名规范
```java
// 变量使用camelCase命名
public class WorkOrderService {
    
    // 常量 - 全大写，下划线分隔
    private static final String DEFAULT_STATUS = "PENDING";
    private static final int MAX_RETRY_COUNT = 3;
    private static final long TIMEOUT_MILLISECONDS = 30000L;
    
    // 成员变量
    private final WorkOrderRepository workOrderRepository;
    private final NotificationService notificationService;
    private final AuditLogger auditLogger;
    
    // 局部变量 - 有意义的名称
    public WorkOrder processWorkOrder(Long workOrderId) {
        WorkOrder workOrder = workOrderRepository.findById(workOrderId);
        String currentStatus = workOrder.getStatus();
        LocalDateTime processStartTime = LocalDateTime.now();
        
        // 集合类型变量
        List<WorkOrderItem> workOrderItems = workOrder.getItems();
        Map<String, Object> processParameters = new HashMap<>();
        Set<String> requiredPermissions = Set.of("WORK_ORDER_EXECUTE");
        
        // 布尔变量 - 使用is/has/can等前缀
        boolean isValidWorkOrder = validateWorkOrder(workOrder);
        boolean hasPermission = checkPermission(requiredPermissions);
        boolean canProcess = isValidWorkOrder && hasPermission;
        
        return workOrder;
    }
}

// 数据库相关命名
@Entity
@Table(name = "work_orders")  // 表名使用下划线，复数形式
public class WorkOrder {
    
    @Id
    @Column(name = "id")
    private Long id;
    
    @Column(name = "order_number")      // 字段名使用下划线
    private String orderNumber;
    
    @Column(name = "created_at")        // 时间字段统一后缀
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### 1.3 代码结构规范

#### 1.3.1 类结构顺序
```java
/**
 * 工单管理服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-15
 */
@Service
@Transactional(readOnly = true)
@Slf4j
public class WorkOrderService {
    
    // 1. 静态常量
    private static final String DEFAULT_STATUS = "PENDING";
    private static final int MAX_RETRY_COUNT = 3;
    
    // 2. 成员变量 (依赖注入)
    private final WorkOrderRepository workOrderRepository;
    private final NotificationService notificationService;
    private final EventPublisher eventPublisher;
    
    // 3. 构造函数
    public WorkOrderService(
            WorkOrderRepository workOrderRepository,
            NotificationService notificationService,
            EventPublisher eventPublisher) {
        this.workOrderRepository = workOrderRepository;
        this.notificationService = notificationService;
        this.eventPublisher = eventPublisher;
    }
    
    // 4. 公共方法 (按逻辑分组)
    
    // 查询相关方法
    public WorkOrder getWorkOrderById(Long id) {
        // 方法实现
    }
    
    public Page<WorkOrder> queryWorkOrders(WorkOrderQuery query) {
        // 方法实现
    }
    
    // 创建相关方法
    @Transactional
    public WorkOrder createWorkOrder(WorkOrderRequest request) {
        // 方法实现
    }
    
    // 更新相关方法
    @Transactional
    public WorkOrder updateWorkOrder(Long id, WorkOrderRequest request) {
        // 方法实现
    }
    
    // 删除相关方法
    @Transactional
    public void deleteWorkOrder(Long id) {
        // 方法实现
    }
    
    // 5. 私有方法
    private void validateWorkOrder(WorkOrder workOrder) {
        // 私有方法实现
    }
    
    private void sendNotification(WorkOrder workOrder, String event) {
        // 私有方法实现
    }
    
    // 6. 静态方法
    public static boolean isValidStatus(String status) {
        // 静态方法实现
    }
    
    // 7. 内部类 (如果需要)
    private static class WorkOrderValidator {
        // 内部类实现
    }
}
```

#### 1.3.2 方法结构规范
```java
/**
 * 创建工单
 * 
 * @param request 工单创建请求
 * @return 创建的工单
 * @throws IllegalArgumentException 当请求参数无效时
 * @throws BusinessException 当业务规则验证失败时
 */
@Transactional
public WorkOrder createWorkOrder(WorkOrderRequest request) {
    // 1. 参数验证
    Assert.notNull(request, "工单请求不能为空");
    Assert.hasText(request.getProductCode(), "产品编码不能为空");
    
    // 2. 业务验证
    validateBusinessRules(request);
    
    // 3. 数据准备
    WorkOrder workOrder = new WorkOrder();
    workOrder.setOrderNumber(generateOrderNumber());
    workOrder.setProductCode(request.getProductCode());
    workOrder.setQuantity(request.getQuantity());
    workOrder.setStatus(DEFAULT_STATUS);
    workOrder.setCreatedAt(LocalDateTime.now());
    
    // 4. 核心业务逻辑
    try {
        workOrder = workOrderRepository.save(workOrder);
        
        // 5. 后置处理
        eventPublisher.publishEvent(new WorkOrderCreatedEvent(workOrder));
        sendNotification(workOrder, "CREATED");
        
        log.info("工单创建成功: {}", workOrder.getOrderNumber());
        
        return workOrder;
        
    } catch (DataIntegrityViolationException e) {
        log.error("工单创建失败，数据完整性约束违反: {}", e.getMessage());
        throw new BusinessException("工单编号已存在", e);
    } catch (Exception e) {
        log.error("工单创建失败: {}", e.getMessage(), e);
        throw new SystemException("工单创建失败", e);
    }
}

// 私有方法 - 业务规则验证
private void validateBusinessRules(WorkOrderRequest request) {
    // 验证产品是否存在
    if (!productService.existsByCode(request.getProductCode())) {
        throw new BusinessException("产品不存在: " + request.getProductCode());
    }
    
    // 验证数量范围
    if (request.getQuantity() <= 0) {
        throw new BusinessException("工单数量必须大于0");
    }
    
    // 验证生产线可用性
    if (!productionLineService.isAvailable(request.getProductionLineId())) {
        throw new BusinessException("生产线不可用");
    }
}
```

### 1.4 注释规范

#### 1.4.1 类注释规范
```java
/**
 * 工单管理服务
 * 
 * <p>提供工单的完整生命周期管理，包括创建、执行、监控和完成等功能。
 * 支持多种工单类型：生产工单、维护工单、检验工单等。</p>
 * 
 * <p>主要功能：
 * <ul>
 *   <li>工单创建和分配</li>
 *   <li>工单执行状态跟踪</li>
 *   <li>工单完成度监控</li>
 *   <li>工单异常处理</li>
 * </ul>
 * </p>
 * 
 * <p>使用示例：
 * <pre>{@code
 * WorkOrderService service = new WorkOrderService(repository, notificationService);
 * WorkOrder workOrder = service.createWorkOrder(request);
 * service.startWorkOrder(workOrder.getId());
 * }</pre>
 * </p>
 * 
 * <AUTHOR>
 * @version 1.2.0
 * @since 2024-01-15
 * @see WorkOrder
 * @see WorkOrderRepository
 */
@Service
public class WorkOrderService {
    // 类实现
}
```

#### 1.4.2 方法注释规范
```java
/**
 * 根据查询条件分页查询工单
 * 
 * <p>支持多种查询条件的组合，包括状态、产品编码、时间范围等。
 * 查询结果按创建时间倒序排列。</p>
 * 
 * @param query 查询条件对象，包含分页参数和过滤条件
 *              <ul>
 *                <li>page - 页码，从0开始</li>
 *                <li>size - 每页大小，最大100</li>
 *                <li>status - 工单状态，可选</li>
 *                <li>productCode - 产品编码，支持模糊匹配</li>
 *                <li>startDate - 开始日期，可选</li>
 *                <li>endDate - 结束日期，可选</li>
 *              </ul>
 * @return 分页工单列表，包含总数和当前页数据
 * @throws IllegalArgumentException 当查询参数无效时抛出
 * @throws DataAccessException 当数据库访问异常时抛出
 * 
 * @since 1.0.0
 * @see WorkOrderQuery
 * @see Page
 */
@Transactional(readOnly = true)
public Page<WorkOrder> queryWorkOrders(WorkOrderQuery query) {
    // 参数验证
    Assert.notNull(query, "查询条件不能为空");
    Assert.isTrue(query.getSize() <= 100, "每页大小不能超过100");
    
    // 构建查询条件
    Specification<WorkOrder> spec = WorkOrderSpecification.build(query);
    Pageable pageable = PageRequest.of(query.getPage(), query.getSize());
    
    // 执行查询
    return workOrderRepository.findAll(spec, pageable);
}
```

#### 1.4.3 复杂业务逻辑注释
```java
/**
 * 工单状态流转处理
 */
@Transactional
public WorkOrder changeWorkOrderStatus(Long workOrderId, String newStatus, String reason) {
    WorkOrder workOrder = getWorkOrderById(workOrderId);
    String currentStatus = workOrder.getStatus();
    
    // 验证状态流转是否合法
    // 状态流转规则：PENDING -> STARTED -> IN_PROGRESS -> COMPLETED
    //              PENDING -> CANCELLED (任何时候都可以取消)
    //              IN_PROGRESS -> SUSPENDED (可以暂停)
    //              SUSPENDED -> IN_PROGRESS (可以恢复)
    if (!isValidStatusTransition(currentStatus, newStatus)) {
        throw new BusinessException(
            String.format("不允许从状态 %s 转换到 %s", currentStatus, newStatus));
    }
    
    // 特殊状态处理逻辑
    switch (newStatus) {
        case "STARTED":
            // 开始工单时需要分配资源和设备
            allocateResources(workOrder);
            validateEquipmentAvailability(workOrder);
            break;
            
        case "COMPLETED":
            // 完成工单时需要验证质量和更新库存
            validateQualityRequirements(workOrder);
            updateInventory(workOrder);
            releaseResources(workOrder);
            break;
            
        case "CANCELLED":
            // 取消工单时需要释放已分配的资源
            releaseResources(workOrder);
            handleCancellationCompensation(workOrder);
            break;
    }
    
    // 更新工单状态
    workOrder.setStatus(newStatus);
    workOrder.setStatusChangeReason(reason);
    workOrder.setStatusChangedAt(LocalDateTime.now());
    
    return workOrderRepository.save(workOrder);
}
```

### 1.5 异常处理规范

#### 1.5.1 异常层次结构
```java
// 基础异常类
public abstract class BaseException extends RuntimeException {
    private final String errorCode;
    private final String errorMessage;
    
    protected BaseException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    
    // getter方法
}

// 业务异常
public class BusinessException extends BaseException {
    public BusinessException(String message) {
        super("BUSINESS_ERROR", message);
    }
    
    public BusinessException(String code, String message) {
        super(code, message);
    }
    
    public BusinessException(String message, Throwable cause) {
        super("BUSINESS_ERROR", message);
        initCause(cause);
    }
}

// 系统异常
public class SystemException extends BaseException {
    public SystemException(String message) {
        super("SYSTEM_ERROR", message);
    }
    
    public SystemException(String message, Throwable cause) {
        super("SYSTEM_ERROR", message);
        initCause(cause);
    }
}

// 具体业务异常
public class WorkOrderNotFoundException extends BusinessException {
    public WorkOrderNotFoundException(Long id) {
        super("WORK_ORDER_NOT_FOUND", "工单不存在: " + id);
    }
}

public class InvalidWorkOrderStatusException extends BusinessException {
    public InvalidWorkOrderStatusException(String currentStatus, String targetStatus) {
        super("INVALID_STATUS_TRANSITION", 
              String.format("不能从状态 %s 转换到 %s", currentStatus, targetStatus));
    }
}
```

#### 1.5.2 异常处理模式
```java
@Service
@Slf4j
public class WorkOrderService {
    
    /**
     * 服务层异常处理 - 捕获并转换异常
     */
    @Transactional
    public WorkOrder createWorkOrder(WorkOrderRequest request) {
        try {
            // 1. 参数验证 - 抛出IllegalArgumentException
            validateRequest(request);
            
            // 2. 业务验证 - 抛出BusinessException
            validateBusinessRules(request);
            
            // 3. 数据操作 - 可能抛出DataAccessException
            WorkOrder workOrder = buildWorkOrder(request);
            workOrder = workOrderRepository.save(workOrder);
            
            // 4. 后置处理 - 可能抛出各种异常
            processWorkOrderCreated(workOrder);
            
            return workOrder;
            
        } catch (BusinessException e) {
            // 业务异常直接重新抛出
            log.warn("工单创建业务异常: {}", e.getMessage());
            throw e;
            
        } catch (DataIntegrityViolationException e) {
            // 数据库约束异常转换为业务异常
            log.error("工单创建数据完整性异常: {}", e.getMessage());
            throw new BusinessException("工单编号已存在或违反数据约束", e);
            
        } catch (DataAccessException e) {
            // 数据库访问异常转换为系统异常
            log.error("工单创建数据访问异常: {}", e.getMessage(), e);
            throw new SystemException("数据库操作失败", e);
            
        } catch (Exception e) {
            // 其他未知异常转换为系统异常
            log.error("工单创建未知异常: {}", e.getMessage(), e);
            throw new SystemException("系统内部错误", e);
        }
    }
    
    /**
     * 私有方法异常处理 - 抛出具体异常
     */
    private void validateBusinessRules(WorkOrderRequest request) {
        // 产品验证
        Product product = productService.findByCode(request.getProductCode());
        if (product == null) {
            throw new BusinessException("PRODUCT_NOT_FOUND", "产品不存在: " + request.getProductCode());
        }
        
        // 生产线验证
        if (!productionLineService.isAvailable(request.getProductionLineId())) {
            throw new BusinessException("PRODUCTION_LINE_UNAVAILABLE", "生产线不可用");
        }
        
        // 库存验证
        if (!inventoryService.hasEnoughMaterial(request.getMaterialRequirements())) {
            throw new BusinessException("INSUFFICIENT_MATERIAL", "原料库存不足");
        }
    }
}
```

#### 1.5.3 全局异常处理
```java
/**
 * 全局异常处理器
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    /**
     * 业务异常处理
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        
        ErrorResponse response = ErrorResponse.builder()
                .code(e.getErrorCode())
                .message(e.getErrorMessage())
                .timestamp(LocalDateTime.now())
                .build();
                
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 系统异常处理
     */
    @ExceptionHandler(SystemException.class)
    public ResponseEntity<ErrorResponse> handleSystemException(SystemException e) {
        log.error("系统异常: {}", e.getMessage(), e);
        
        ErrorResponse response = ErrorResponse.builder()
                .code("SYSTEM_ERROR")
                .message("系统内部错误，请联系管理员")
                .timestamp(LocalDateTime.now())
                .build();
                
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 参数验证异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证异常: {}", e.getMessage());
        
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
                
        ErrorResponse response = ErrorResponse.builder()
                .code("VALIDATION_ERROR")
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
                
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 未知异常处理
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleUnknownException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        
        ErrorResponse response = ErrorResponse.builder()
                .code("UNKNOWN_ERROR")
                .message("系统异常，请联系管理员")
                .timestamp(LocalDateTime.now())
                .build();
                
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}

/**
 * 错误响应对象
 */
@Data
@Builder
public class ErrorResponse {
    private String code;
    private String message;
    private LocalDateTime timestamp;
    private String path;
    private String traceId;
}
```

### 1.6 日志规范

#### 1.6.1 日志级别使用
```java
@Service
@Slf4j
public class WorkOrderService {
    
    public WorkOrder createWorkOrder(WorkOrderRequest request) {
        // TRACE - 非常详细的调试信息，生产环境不启用
        log.trace("开始创建工单，请求参数: {}", request);
        
        // DEBUG - 调试信息，用于开发和测试环境
        log.debug("验证工单创建参数: productCode={}, quantity={}", 
                  request.getProductCode(), request.getQuantity());
        
        try {
            WorkOrder workOrder = buildWorkOrder(request);
            workOrder = workOrderRepository.save(workOrder);
            
            // INFO - 重要的业务流程信息
            log.info("工单创建成功: orderNumber={}, productCode={}, quantity={}", 
                     workOrder.getOrderNumber(), workOrder.getProductCode(), workOrder.getQuantity());
            
            return workOrder;
            
        } catch (BusinessException e) {
            // WARN - 业务异常，需要关注但不是系统错误
            log.warn("工单创建失败，业务规则验证不通过: {}, productCode={}", 
                     e.getMessage(), request.getProductCode());
            throw e;
            
        } catch (Exception e) {
            // ERROR - 系统错误，需要立即处理
            log.error("工单创建失败，系统异常: productCode={}, error={}", 
                      request.getProductCode(), e.getMessage(), e);
            throw new SystemException("工单创建失败", e);
        }
    }
    
    public void processWorkOrderBatch(List<WorkOrderRequest> requests) {
        log.info("开始批量处理工单，数量: {}", requests.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (WorkOrderRequest request : requests) {
            try {
                createWorkOrder(request);
                successCount++;
                
                // 批量处理中的成功记录使用DEBUG级别
                log.debug("工单创建成功: {}", request.getProductCode());
                
            } catch (Exception e) {
                failureCount++;
                
                // 批量处理中的失败记录使用WARN级别
                log.warn("工单创建失败: productCode={}, error={}", 
                         request.getProductCode(), e.getMessage());
            }
        }
        
        // 批量处理结果使用INFO级别
        log.info("批量工单处理完成，成功: {}, 失败: {}", successCount, failureCount);
    }
}
```

#### 1.6.2 日志格式规范
```java
@Service
@Slf4j
public class WorkOrderService {
    
    /**
     * 标准日志格式示例
     */
    public void demonstrateLoggingFormats() {
        String userId = "user123";
        String workOrderId = "WO202401150001";
        
        // 1. 业务操作日志 - 包含关键业务信息
        log.info("用户操作: userId={}, action=CREATE_WORK_ORDER, workOrderId={}, result=SUCCESS", 
                 userId, workOrderId);
        
        // 2. 性能监控日志 - 包含执行时间
        long startTime = System.currentTimeMillis();
        // ... 业务逻辑 ...
        long duration = System.currentTimeMillis() - startTime;
        log.info("性能监控: operation=CREATE_WORK_ORDER, workOrderId={}, duration={}ms", 
                 workOrderId, duration);
        
        // 3. 外部系统调用日志 - 包含调用信息
        log.info("外部调用: system=ERP, operation=SYNC_PRODUCT, productCode={}, duration={}ms, result={}", 
                 "PROD001", 150, "SUCCESS");
        
        // 4. 数据变更日志 - 包含变更前后的值
        log.info("数据变更: table=work_orders, id={}, field=status, oldValue={}, newValue={}, userId={}", 
                 workOrderId, "PENDING", "STARTED", userId);
        
        // 5. 安全相关日志 - 包含安全事件信息
        log.warn("安全事件: event=UNAUTHORIZED_ACCESS, userId={}, resource={}, ip={}", 
                 userId, "/api/work-orders/sensitive", "*************");
        
        // 6. 错误日志 - 包含完整的错误上下文
        try {
            // 可能抛出异常的代码
        } catch (Exception e) {
            log.error("业务异常: operation=CREATE_WORK_ORDER, workOrderId={}, userId={}, error={}", 
                      workOrderId, userId, e.getMessage(), e);
        }
    }
    
    /**
     * 结构化日志示例 - 使用MDC传递上下文信息
     */
    public WorkOrder createWorkOrderWithContext(WorkOrderRequest request) {
        // 设置MDC上下文信息
        MDC.put("userId", getCurrentUserId());
        MDC.put("requestId", generateRequestId());
        MDC.put("operation", "CREATE_WORK_ORDER");
        
        try {
            log.info("开始创建工单: productCode={}", request.getProductCode());
            
            WorkOrder workOrder = doCreateWorkOrder(request);
            
            // 在MDC中添加生成的工单ID
            MDC.put("workOrderId", workOrder.getOrderNumber());
            log.info("工单创建成功");
            
            return workOrder;
            
        } catch (Exception e) {
            log.error("工单创建失败: {}", e.getMessage(), e);
            throw e;
            
        } finally {
            // 清理MDC上下文
            MDC.clear();
        }
    }
}
```

#### 1.6.3 敏感信息处理
```java
@Service
@Slf4j
public class UserService {
    
    /**
     * 敏感信息脱敏处理
     */
    public void demonstrateSensitiveDataLogging() {
        String email = "<EMAIL>";
        String phone = "13800138000";
        String idCard = "110101199001011234";
        String password = "secretPassword";
        
        // ❌ 错误 - 直接记录敏感信息
        // log.info("用户登录: email={}, password={}", email, password);
        
        // ✅ 正确 - 脱敏处理
        log.info("用户登录: email={}, phone={}, idCard={}", 
                 maskEmail(email), maskPhone(phone), maskIdCard(idCard));
        
        // 密码等敏感信息永远不记录
        log.info("密码验证: email={}, result={}", maskEmail(email), "SUCCESS");
    }
    
    /**
     * 邮箱脱敏
     */
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return "***";
        }
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 3) {
            return "***@" + domain;
        }
        
        return username.substring(0, 2) + "***" + username.substring(username.length() - 1) + "@" + domain;
    }
    
    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return "***";
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    /**
     * 身份证号脱敏
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return "***";
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
}
```

---

## 第二部分：Spring Boot开发规范

### 2.1 项目配置规范

#### 2.1.1 配置文件结构
```yaml
# application.yml - 主配置文件
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  application:
    name: mes-mdm-platform
    
# application-dev.yml - 开发环境配置
spring:
  datasource:
    url: ****************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        
logging:
  level:
    com.company.mes: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    
# application-prod.yml - 生产环境配置
spring:
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      
logging:
  level:
    root: WARN
    com.company.mes: INFO
  file:
    name: /var/log/mes-mdm/application.log
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
```

#### 2.1.2 配置类规范
```java
/**
 * 数据库配置
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.company.mes.infrastructure.persistence.repository")
@EnableTransactionManagement
public class DatabaseConfig {
    
    /**
     * JPA审计配置
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> Optional.ofNullable(SecurityContextHolder.getContext())
                .map(SecurityContext::getAuthentication)
                .filter(Authentication::isAuthenticated)
                .map(Authentication::getName);
    }
    
    /**
     * 数据库连接池监控
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.hikari.register-mbeans", havingValue = "true")
    public DataSourceHealthIndicator dataSourceHealthIndicator(DataSource dataSource) {
        return new DataSourceHealthIndicator(dataSource);
    }
}

/**
 * 安全配置
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {
    
    private final JwtAuthenticationEntryPoint authenticationEntryPoint;
    private final JwtAccessDeniedHandler accessDeniedHandler;
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler)
            )
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/public/**").permitAll()
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
            
        return http.build();
    }
}

/**
 * Redis配置
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "true", matchIfMissing = true)
public class RedisConfig {
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(objectMapper);
        
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
                
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .build();
    }
}
```

### 2.2 Controller层规范

#### 2.2.1 REST API设计规范
```java
/**
 * 工单管理控制器
 */
@RestController
@RequestMapping("/api/v1/work-orders")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "工单管理", description = "工单相关操作接口")
public class WorkOrderController {
    
    private final WorkOrderService workOrderService;
    
    /**
     * 分页查询工单
     */
    @GetMapping
    @Operation(summary = "分页查询工单", description = "根据条件分页查询工单列表")
    @PreAuthorize("hasAuthority('WORK_ORDER:READ')")
    public ResponseEntity<ApiResponse<Page<WorkOrderDTO>>> queryWorkOrders(
            @Valid @ModelAttribute WorkOrderQueryRequest request) {
        
        log.info("查询工单列表: {}", request);
        
        WorkOrderQuery query = WorkOrderQuery.builder()
                .page(request.getPage())
                .size(request.getSize())
                .status(request.getStatus())
                .productCode(request.getProductCode())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .build();
                
        Page<WorkOrder> workOrders = workOrderService.queryWorkOrders(query);
        Page<WorkOrderDTO> result = workOrders.map(WorkOrderDTO::fromEntity);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 根据ID查询工单
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询工单详情", description = "根据ID查询工单详细信息")
    @PreAuthorize("hasAuthority('WORK_ORDER:READ')")
    public ResponseEntity<ApiResponse<WorkOrderDTO>> getWorkOrder(
            @PathVariable @NotNull @Min(1) Long id) {
        
        log.info("查询工单详情: id={}", id);
        
        WorkOrder workOrder = workOrderService.getWorkOrderById(id);
        WorkOrderDTO result = WorkOrderDTO.fromEntity(workOrder);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 创建工单
     */
    @PostMapping
    @Operation(summary = "创建工单", description = "创建新的工单")
    @PreAuthorize("hasAuthority('WORK_ORDER:CREATE')")
    public ResponseEntity<ApiResponse<WorkOrderDTO>> createWorkOrder(
            @Valid @RequestBody WorkOrderCreateRequest request) {
        
        log.info("创建工单: {}", request);
        
        WorkOrder workOrder = workOrderService.createWorkOrder(request);
        WorkOrderDTO result = WorkOrderDTO.fromEntity(workOrder);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(result));
    }
    
    /**
     * 更新工单
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新工单", description = "更新工单信息")
    @PreAuthorize("hasAuthority('WORK_ORDER:UPDATE')")
    public ResponseEntity<ApiResponse<WorkOrderDTO>> updateWorkOrder(
            @PathVariable @NotNull @Min(1) Long id,
            @Valid @RequestBody WorkOrderUpdateRequest request) {
        
        log.info("更新工单: id={}, request={}", id, request);
        
        WorkOrder workOrder = workOrderService.updateWorkOrder(id, request);
        WorkOrderDTO result = WorkOrderDTO.fromEntity(workOrder);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 删除工单
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除工单", description = "删除指定工单")
    @PreAuthorize("hasAuthority('WORK_ORDER:DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteWorkOrder(
            @PathVariable @NotNull @Min(1) Long id) {
        
        log.info("删除工单: id={}", id);
        
        workOrderService.deleteWorkOrder(id);
        
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    /**
     * 工单状态变更
     */
    @PatchMapping("/{id}/status")
    @Operation(summary = "变更工单状态", description = "变更工单状态")
    @PreAuthorize("hasAuthority('WORK_ORDER:UPDATE_STATUS')")
    public ResponseEntity<ApiResponse<WorkOrderDTO>> changeWorkOrderStatus(
            @PathVariable @NotNull @Min(1) Long id,
            @Valid @RequestBody WorkOrderStatusChangeRequest request) {
        
        log.info("变更工单状态: id={}, request={}", id, request);
        
        WorkOrder workOrder = workOrderService.changeWorkOrderStatus(id, request.getStatus(), request.getReason());
        WorkOrderDTO result = WorkOrderDTO.fromEntity(workOrder);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 批量操作工单
     */
    @PostMapping("/batch")
    @Operation(summary = "批量操作工单", description = "批量创建或更新工单")
    @PreAuthorize("hasAuthority('WORK_ORDER:BATCH')")
    public ResponseEntity<ApiResponse<BatchOperationResult>> batchOperation(
            @Valid @RequestBody WorkOrderBatchRequest request) {
        
        log.info("批量操作工单: operation={}, count={}", request.getOperation(), request.getWorkOrders().size());
        
        BatchOperationResult result = workOrderService.batchOperation(request);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
}
```

#### 2.2.2 统一响应格式
```java
/**
 * 统一API响应格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .code("SUCCESS")
                .message("操作成功")
                .data(data)
                .timestamp(LocalDateTime.now())
                .requestId(getRequestId())
                .build();
    }
    
    /**
     * 成功响应 - 无数据
     */
    public static <T> ApiResponse<T> success() {
        return success(null);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String code, String message) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .timestamp(LocalDateTime.now())
                .requestId(getRequestId())
                .build();
    }
    
    /**
     * 获取请求ID
     */
    private static String getRequestId() {
        return MDC.get("requestId");
    }
}

/**
 * 分页响应格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    
    /**
     * 数据列表
     */
    private List<T> content;
    
    /**
     * 当前页码
     */
    private int page;
    
    /**
     * 每页大小
     */
    private int size;
    
    /**
     * 总元素数
     */
    private long totalElements;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 是否第一页
     */
    private boolean first;
    
    /**
     * 是否最后一页
     */
    private boolean last;
    
    /**
     * 从Spring Data Page转换
     */
    public static <T> PageResponse<T> from(Page<T> page) {
        return PageResponse.<T>builder()
                .content(page.getContent())
                .page(page.getNumber())
                .size(page.getSize())
                .totalElements(page.getTotalElements())
                .totalPages(page.getTotalPages())
                .first(page.isFirst())
                .last(page.isLast())
                .build();
    }
}
```

### 2.3 Service层规范

#### 2.3.1 服务层架构
```java
/**
 * 应用服务接口
 */
public interface WorkOrderApplicationService {
    
    /**
     * 创建工单
     */
    WorkOrder createWorkOrder(WorkOrderCreateCommand command);
    
    /**
     * 更新工单
     */
    WorkOrder updateWorkOrder(WorkOrderUpdateCommand command);
    
    /**
     * 查询工单
     */
    WorkOrder getWorkOrderById(Long id);
    
    /**
     * 分页查询工单
     */
    Page<WorkOrder> queryWorkOrders(WorkOrderQuery query);
}

/**
 * 应用服务实现
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class WorkOrderApplicationServiceImpl implements WorkOrderApplicationService {
    
    private final WorkOrderDomainService workOrderDomainService;
    private final WorkOrderRepository workOrderRepository;
    private final ProductRepository productRepository;
    private final EventPublisher eventPublisher;
    
    @Override
    public WorkOrder createWorkOrder(WorkOrderCreateCommand command) {
        log.info("开始创建工单: {}", command);
        
        // 1. 领域对象构建
        Product product = productRepository.findByCode(command.getProductCode())
                .orElseThrow(() -> new BusinessException("产品不存在: " + command.getProductCode()));
        
        WorkOrder workOrder = WorkOrder.create(
                command.getOrderNumber(),
                product,
                command.getQuantity(),
                command.getPlannedStartDate(),
                command.getPlannedEndDate()
        );
        
        // 2. 领域服务验证
        workOrderDomainService.validateWorkOrderCreation(workOrder);
        
        // 3. 持久化
        workOrder = workOrderRepository.save(workOrder);
        
        // 4. 发布领域事件
        eventPublisher.publishEvent(new WorkOrderCreatedEvent(workOrder));
        
        log.info("工单创建成功: {}", workOrder.getOrderNumber());
        return workOrder;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<WorkOrder> queryWorkOrders(WorkOrderQuery query) {
        log.debug("查询工单列表: {}", query);
        
        // 构建查询规格
        Specification<WorkOrder> spec = WorkOrderSpecifications.build(query);
        Pageable pageable = PageRequest.of(query.getPage(), query.getSize());
        
        return workOrderRepository.findAll(spec, pageable);
    }
}

/**
 * 领域服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WorkOrderDomainService {
    
    private final ProductionLineRepository productionLineRepository;
    private final MaterialRepository materialRepository;
    
    /**
     * 验证工单创建
     */
    public void validateWorkOrderCreation(WorkOrder workOrder) {
        // 验证生产线可用性
        validateProductionLineAvailability(workOrder);
        
        // 验证原料可用性
        validateMaterialAvailability(workOrder);
        
        // 验证时间冲突
        validateTimeConflict(workOrder);
    }
    
    /**
     * 计算工单优先级
     */
    public int calculateWorkOrderPriority(WorkOrder workOrder) {
        int priority = 0;
        
        // 基于客户优先级
        priority += workOrder.getCustomer().getPriorityWeight() * 10;
        
        // 基于紧急程度
        priority += workOrder.getUrgencyLevel().getWeight() * 5;
        
        // 基于交期
        priority += calculateDeadlinePriority(workOrder.getPlannedEndDate());
        
        return priority;
    }
    
    private void validateProductionLineAvailability(WorkOrder workOrder) {
        // 实现生产线可用性验证逻辑
    }
    
    private void validateMaterialAvailability(WorkOrder workOrder) {
        // 实现原料可用性验证逻辑
    }
    
    private void validateTimeConflict(WorkOrder workOrder) {
        // 实现时间冲突验证逻辑
    }
    
    private int calculateDeadlinePriority(LocalDateTime deadline) {
        // 实现交期优先级计算逻辑
        return 0;
    }
}
```

---

## 第三部分：数据库开发规范

### 3.1 实体设计规范

#### 3.1.1 JPA实体规范
```java
/**
 * 工单实体
 */
@Entity
@Table(name = "work_orders", indexes = {
    @Index(name = "idx_work_orders_status", columnList = "status"),
    @Index(name = "idx_work_orders_product_code", columnList = "product_code"),
    @Index(name = "idx_work_orders_created_at", columnList = "created_at")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(exclude = {"items", "materials"})
public class WorkOrder {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;
    
    /**
     * 工单编号 - 业务唯一标识
     */
    @Column(name = "order_number", nullable = false, unique = true, length = 50)
    @NotBlank(message = "工单编号不能为空")
    @Size(max = 50, message = "工单编号长度不能超过50")
    private String orderNumber;
    
    /**
     * 产品编码
     */
    @Column(name = "product_code", nullable = false, length = 100)
    @NotBlank(message = "产品编码不能为空")
    @Size(max = 100, message = "产品编码长度不能超过100")
    private String productCode;
    
    /**
     * 产品名称
     */
    @Column(name = "product_name", nullable = false, length = 200)
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 200, message = "产品名称长度不能超过200")
    private String productName;
    
    /**
     * 计划数量
     */
    @Column(name = "planned_quantity", nullable = false)
    @NotNull(message = "计划数量不能为空")
    @Min(value = 1, message = "计划数量必须大于0")
    private Integer plannedQuantity;
    
    /**
     * 实际数量
     */
    @Column(name = "actual_quantity")
    @Min(value = 0, message = "实际数量不能小于0")
    private Integer actualQuantity;
    
    /**
     * 工单状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @NotNull(message = "工单状态不能为空")
    private WorkOrderStatus status;
    
    /**
     * 优先级
     */
    @Column(name = "priority")
    @Min(value = 1, message = "优先级最小值为1")
    @Max(value = 10, message = "优先级最大值为10")
    private Integer priority;
    
    /**
     * 计划开始时间
     */
    @Column(name = "planned_start_date")
    private LocalDateTime plannedStartDate;
    
    /**
     * 计划结束时间
     */
    @Column(name = "planned_end_date")
    private LocalDateTime plannedEndDate;
    
    /**
     * 实际开始时间
     */
    @Column(name = "actual_start_date")
    private LocalDateTime actualStartDate;
    
    /**
     * 实际结束时间
     */
    @Column(name = "actual_end_date")
    private LocalDateTime actualEndDate;
    
    /**
     * 备注
     */
    @Column(name = "remarks", length = 1000)
    @Size(max = 1000, message = "备注长度不能超过1000")
    private String remarks;
    
    /**
     * 工单项目列表
     */
    @OneToMany(mappedBy = "workOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sequence")
    private List<WorkOrderItem> items = new ArrayList<>();
    
    /**
     * 物料需求列表
     */
    @OneToMany(mappedBy = "workOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<WorkOrderMaterial> materials = new ArrayList<>();
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "created_by", length = 50, updatable = false)
    private String createdBy;
    
    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 版本号 - 乐观锁
     */
    @Version
    private Long version;
    
    /**
     * 领域方法 - 创建工单
     */
    public static WorkOrder create(String orderNumber, String productCode, String productName, Integer quantity) {
        WorkOrder workOrder = new WorkOrder();
        workOrder.orderNumber = orderNumber;
        workOrder.productCode = productCode;
        workOrder.productName = productName;
        workOrder.plannedQuantity = quantity;
        workOrder.actualQuantity = 0;
        workOrder.status = WorkOrderStatus.PENDING;
        workOrder.priority = 5; // 默认优先级
        
        return workOrder;
    }
    
    /**
     * 领域方法 - 开始工单
     */
    public void start() {
        if (this.status != WorkOrderStatus.PENDING) {
            throw new BusinessException("只有待开始状态的工单才能启动");
        }
        
        this.status = WorkOrderStatus.IN_PROGRESS;
        this.actualStartDate = LocalDateTime.now();
    }
    
    /**
     * 领域方法 - 完成工单
     */
    public void complete(Integer actualQuantity) {
        if (this.status != WorkOrderStatus.IN_PROGRESS) {
            throw new BusinessException("只有进行中状态的工单才能完成");
        }
        
        if (actualQuantity < 0) {
            throw new BusinessException("实际数量不能小于0");
        }
        
        this.status = WorkOrderStatus.COMPLETED;
        this.actualQuantity = actualQuantity;
        this.actualEndDate = LocalDateTime.now();
    }
    
    /**
     * 领域方法 - 暂停工单
     */
    public void suspend(String reason) {
        if (this.status != WorkOrderStatus.IN_PROGRESS) {
            throw new BusinessException("只有进行中状态的工单才能暂停");
        }
        
        this.status = WorkOrderStatus.SUSPENDED;
        this.remarks = "暂停原因: " + reason;
    }
}

/**
 * 工单状态枚举
 */
public enum WorkOrderStatus {
    PENDING("待开始"),
    IN_PROGRESS("进行中"),
    SUSPENDED("已暂停"),
    COMPLETED("已完成"),
    CANCELLED("已取消");
    
    private final String description;
    
    WorkOrderStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

#### 3.1.2 Repository接口规范
```java
/**
 * 工单仓库接口
 */
@Repository
public interface WorkOrderRepository extends JpaRepository<WorkOrder, Long>, JpaSpecificationExecutor<WorkOrder> {
    
    /**
     * 根据工单编号查询
     */
    Optional<WorkOrder> findByOrderNumber(String orderNumber);
    
    /**
     * 检查工单编号是否存在
     */
    boolean existsByOrderNumber(String orderNumber);
    
    /**
     * 根据状态查询工单
     */
    List<WorkOrder> findByStatus(WorkOrderStatus status);
    
    /**
     * 根据产品编码和状态查询
     */
    Page<WorkOrder> findByProductCodeAndStatus(String productCode, WorkOrderStatus status, Pageable pageable);
    
    /**
     * 查询指定时间范围内的工单
     */
    @Query("SELECT w FROM WorkOrder w WHERE w.plannedStartDate >= :startDate AND w.plannedEndDate <= :endDate")
    List<WorkOrder> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    /**
     * 根据优先级查询待开始的工单
     */
    @Query("SELECT w FROM WorkOrder w WHERE w.status = :status ORDER BY w.priority DESC, w.plannedStartDate ASC")
    List<WorkOrder> findPendingWorkOrdersByPriority(@Param("status") WorkOrderStatus status);
    
    /**
     * 统计各状态工单数量
     */
    @Query("SELECT w.status, COUNT(w) FROM WorkOrder w GROUP BY w.status")
    List<Object[]> countByStatus();
    
    /**
     * 查询超期工单
     */
    @Query("SELECT w FROM WorkOrder w WHERE w.status IN :statuses AND w.plannedEndDate < :currentDate")
    List<WorkOrder> findOverdueWorkOrders(@Param("statuses") List<WorkOrderStatus> statuses, @Param("currentDate") LocalDateTime currentDate);
    
    /**
     * 更新工单状态
     */
    @Modifying
    @Query("UPDATE WorkOrder w SET w.status = :newStatus, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id AND w.status = :currentStatus")
    int updateStatus(@Param("id") Long id, @Param("currentStatus") WorkOrderStatus currentStatus, @Param("newStatus") WorkOrderStatus newStatus);
    
    /**
     * 批量更新工单优先级
     */
    @Modifying
    @Query("UPDATE WorkOrder w SET w.priority = :priority WHERE w.id IN :ids")
    int updatePriorityBatch(@Param("ids") List<Long> ids, @Param("priority") Integer priority);
    
    /**
     * 删除指定状态的历史工单
     */
    @Modifying
    @Query("DELETE FROM WorkOrder w WHERE w.status = :status AND w.updatedAt < :cutoffDate")
    int deleteOldWorkOrders(@Param("status") WorkOrderStatus status, @Param("cutoffDate") LocalDateTime cutoffDate);
}
```

#### 3.1.3 Specification查询规范
```java
/**
 * 工单查询规格构建器
 */
@Component
public class WorkOrderSpecifications {
    
    /**
     * 根据查询条件构建Specification
     */
    public static Specification<WorkOrder> build(WorkOrderQuery query) {
        return Specification.where(null)
                .and(hasStatus(query.getStatus()))
                .and(hasProductCode(query.getProductCode()))
                .and(hasProductName(query.getProductName()))
                .and(hasOrderNumber(query.getOrderNumber()))
                .and(hasPriorityBetween(query.getMinPriority(), query.getMaxPriority()))
                .and(hasPlannedStartDateBetween(query.getStartDate(), query.getEndDate()))
                .and(hasCreatedBy(query.getCreatedBy()))
                .and(hasCreatedAtBetween(query.getCreatedStartDate(), query.getCreatedEndDate()));
    }
    
    /**
     * 状态条件
     */
    public static Specification<WorkOrder> hasStatus(WorkOrderStatus status) {
        return (root, query, criteriaBuilder) -> {
            if (status == null) {
                return null;
            }
            return criteriaBuilder.equal(root.get("status"), status);
        };
    }
    
    /**
     * 产品编码条件 - 支持模糊匹配
     */
    public static Specification<WorkOrder> hasProductCode(String productCode) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(productCode)) {
                return null;
            }
            return criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("productCode")), 
                    "%" + productCode.toLowerCase() + "%"
            );
        };
    }
    
    /**
     * 产品名称条件 - 支持模糊匹配
     */
    public static Specification<WorkOrder> hasProductName(String productName) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(productName)) {
                return null;
            }
            return criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("productName")), 
                    "%" + productName.toLowerCase() + "%"
            );
        };
    }
    
    /**
     * 工单编号条件 - 支持模糊匹配
     */
    public static Specification<WorkOrder> hasOrderNumber(String orderNumber) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(orderNumber)) {
                return null;
            }
            return criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("orderNumber")), 
                    "%" + orderNumber.toLowerCase() + "%"
            );
        };
    }
    
    /**
     * 优先级范围条件
     */
    public static Specification<WorkOrder> hasPriorityBetween(Integer minPriority, Integer maxPriority) {
        return (root, query, criteriaBuilder) -> {
            if (minPriority == null && maxPriority == null) {
                return null;
            }
            
            if (minPriority != null && maxPriority != null) {
                return criteriaBuilder.between(root.get("priority"), minPriority, maxPriority);
            } else if (minPriority != null) {
                return criteriaBuilder.greaterThanOrEqualTo(root.get("priority"), minPriority);
            } else {
                return criteriaBuilder.lessThanOrEqualTo(root.get("priority"), maxPriority);
            }
        };
    }
    
    /**
     * 计划开始时间范围条件
     */
    public static Specification<WorkOrder> hasPlannedStartDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return (root, query, criteriaBuilder) -> {
            if (startDate == null && endDate == null) {
                return null;
            }
            
            Path<LocalDateTime> plannedStartDate = root.get("plannedStartDate");
            
            if (startDate != null && endDate != null) {
                return criteriaBuilder.between(plannedStartDate, startDate, endDate);
            } else if (startDate != null) {
                return criteriaBuilder.greaterThanOrEqualTo(plannedStartDate, startDate);
            } else {
                return criteriaBuilder.lessThanOrEqualTo(plannedStartDate, endDate);
            }
        };
    }
    
    /**
     * 创建人条件
     */
    public static Specification<WorkOrder> hasCreatedBy(String createdBy) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(createdBy)) {
                return null;
            }
            return criteriaBuilder.equal(root.get("createdBy"), createdBy);
        };
    }
    
    /**
     * 创建时间范围条件
     */
    public static Specification<WorkOrder> hasCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return (root, query, criteriaBuilder) -> {
            if (startDate == null && endDate == null) {
                return null;
            }
            
            Path<LocalDateTime> createdAt = root.get("createdAt");
            
            if (startDate != null && endDate != null) {
                return criteriaBuilder.between(createdAt, startDate, endDate);
            } else if (startDate != null) {
                return criteriaBuilder.greaterThanOrEqualTo(createdAt, startDate);
            } else {
                return criteriaBuilder.lessThanOrEqualTo(createdAt, endDate);
            }
        };
    }
    
    /**
     * 多状态条件
     */
    public static Specification<WorkOrder> hasStatusIn(List<WorkOrderStatus> statuses) {
        return (root, query, criteriaBuilder) -> {
            if (statuses == null || statuses.isEmpty()) {
                return null;
            }
            return root.get("status").in(statuses);
        };
    }
    
    /**
     * 排除指定状态
     */
    public static Specification<WorkOrder> statusNotIn(List<WorkOrderStatus> statuses) {
        return (root, query, criteriaBuilder) -> {
            if (statuses == null || statuses.isEmpty()) {
                return null;
            }
            return criteriaBuilder.not(root.get("status").in(statuses));
        };
    }
}
```

---

**续写其他规范部分...**

---

**文档信息**:
- **版本**: v1.0
- **创建日期**: 2024年1月
- **创建人**: 技术架构组
- **审核人**: 技术委员会
- **适用项目**: MES/MDM平台项目

**变更记录**:
| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| v1.0 | 2024-01-15 | 初始版本创建 | 技术架构师 |

---

*本规范为项目开发的基础标准，所有开发人员必须严格遵循。如有疑问或改进建议，请及时向技术委员会反馈。*
