# 部署运维规范与标准

## 文档概述

### 目的和范围
本文档规定了MES/MDM项目的部署策略、运维流程、监控标准和应急处理规范，确保系统稳定运行、快速部署和高效运维。

### 运维目标
- **可用性**: 系统可用性 ≥ 99.9%
- **性能**: 响应时间和吞吐量满足业务要求
- **安全**: 数据和系统安全得到保障
- **可扩展**: 支持业务快速扩展
- **可恢复**: 故障快速恢复能力

---

## 第一部分：部署架构规范

### 1.1 环境架构设计

#### 1.1.1 环境分层
```yaml
生产环境 (PROD):
  用途: 正式生产运行
  可用性要求: 99.9%
  部署策略: 蓝绿部署
  备份策略: 实时备份 + 定期备份
  监控级别: 全面监控
  
预生产环境 (STAGING):
  用途: 生产前最后验证
  可用性要求: 95%
  部署策略: 滚动部署
  备份策略: 定期备份
  监控级别: 重点监控
  
测试环境 (TEST):
  用途: 功能和集成测试
  可用性要求: 90%
  部署策略: 直接替换
  备份策略: 按需备份
  监控级别: 基础监控
  
开发环境 (DEV):
  用途: 开发和调试
  可用性要求: 不做要求
  部署策略: 自动部署
  备份策略: 不备份
  监控级别: 基础监控
```

#### 1.1.2 容器化部署架构
```yaml
Kubernetes集群架构:
  Master节点 (3个):
    - 高可用配置
    - etcd集群存储
    - API Server负载均衡
    - 控制平面组件
    
  Worker节点 (5-10个):
    - 应用工作负载
    - 按功能分组部署
    - 资源池化管理
    - 弹性扩缩容
    
  存储配置:
    - 持久化存储: NFS/Ceph
    - 配置存储: ConfigMap/Secret
    - 日志存储: ELK Stack
    - 监控存储: Prometheus
    
网络配置:
  Service网络:
    - ClusterIP: 内部服务通信
    - NodePort: 外部访问
    - LoadBalancer: 负载均衡
    - Ingress: HTTP/HTTPS路由
    
  安全策略:
    - NetworkPolicy: 网络隔离
    - SecurityContext: 安全上下文
    - RBAC: 角色权限控制
    - Pod Security: Pod安全策略
```

### 1.2 CI/CD流水线规范

#### 1.2.1 部署流水线设计
```yaml
流水线阶段:
  代码阶段:
    - 代码检出
    - 依赖安装
    - 编译构建
    - 单元测试
    - 代码质量检查
    
  构建阶段:
    - 镜像构建
    - 安全扫描
    - 镜像推送
    - 制品归档
    - 版本标记
    
  部署阶段:
    - 环境准备
    - 配置更新
    - 滚动部署
    - 健康检查
    - 部署验证
    
  验证阶段:
    - 冒烟测试
    - 集成测试
    - 性能验证
    - 安全扫描
    - 用户验收

部署策略:
  蓝绿部署 (生产环境):
    - 两套完全相同的环境
    - 流量瞬间切换
    - 快速回滚能力
    - 零停机部署
    
  滚动部署 (预生产环境):
    - 逐步替换实例
    - 减少资源占用
    - 渐进式验证
    - 自动回滚
    
  金丝雀部署 (特殊场景):
    - 小流量验证
    - 风险评估
    - 逐步扩大范围
    - 数据驱动决策
```

#### 1.2.2 GitLab CI/CD配置示例
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - security
  - deploy-staging
  - deploy-production

variables:
  DOCKER_REGISTRY: "registry.company.com"
  IMAGE_NAME: "$DOCKER_REGISTRY/mes-mdm"
  KUBECTL_VERSION: "1.28.0"

# 构建阶段
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $IMAGE_NAME:$CI_COMMIT_SHA .
    - docker tag $IMAGE_NAME:$CI_COMMIT_SHA $IMAGE_NAME:latest
    - docker push $IMAGE_NAME:$CI_COMMIT_SHA
    - docker push $IMAGE_NAME:latest
  only:
    - main
    - develop

# 安全扫描
security-scan:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy image --exit-code 1 --severity HIGH,CRITICAL $IMAGE_NAME:$CI_COMMIT_SHA
  allow_failure: true

# 部署到预生产环境
deploy-staging:
  stage: deploy-staging
  image: bitnami/kubectl:$KUBECTL_VERSION
  script:
    - kubectl config use-context staging
    - envsubst < k8s/deployment.yaml | kubectl apply -f -
    - kubectl rollout status deployment/mes-mdm-app -n staging
    - kubectl get pods -n staging
  environment:
    name: staging
    url: https://staging.mes-mdm.company.com
  only:
    - develop

# 部署到生产环境
deploy-production:
  stage: deploy-production
  image: bitnami/kubectl:$KUBECTL_VERSION
  script:
    - kubectl config use-context production
    - envsubst < k8s/deployment.yaml | kubectl apply -f -
    - kubectl rollout status deployment/mes-mdm-app -n production
    - ./scripts/health-check.sh
  environment:
    name: production
    url: https://mes-mdm.company.com
  when: manual
  only:
    - main
```

---

## 第二部分：运维流程规范

### 2.1 日常运维管理

#### 2.1.1 监控告警体系
```yaml
监控层次:
  基础设施监控:
    - 服务器资源: CPU、内存、磁盘、网络
    - 容器资源: Pod状态、资源使用
    - 网络监控: 连通性、延迟、带宽
    - 存储监控: 磁盘空间、IO性能
    
  应用服务监控:
    - 服务状态: 健康检查、启动状态
    - 性能指标: 响应时间、吞吐量、错误率
    - 业务指标: 用户数、订单数、收入
    - 依赖监控: 数据库、缓存、消息队列
    
  日志监控:
    - 错误日志: ERROR、FATAL级别日志
    - 安全日志: 登录失败、权限异常
    - 业务日志: 关键业务操作
    - 审计日志: 数据变更、配置修改

告警规则:
  紧急告警 (P0):
    - 服务完全不可用
    - 数据库连接失败
    - 严重安全事件
    - 响应方式: 立即电话 + 短信
    
  重要告警 (P1):
    - 服务性能严重下降
    - 错误率超过阈值
    - 资源使用率过高
    - 响应方式: 微信 + 邮件
    
  一般告警 (P2):
    - 服务性能轻微下降
    - 资源使用率较高
    - 非关键功能异常
    - 响应方式: 邮件通知
    
  提醒告警 (P3):
    - 预警性指标
    - 建议性优化
    - 定期检查项
    - 响应方式: 工作群消息
```

#### 2.1.2 Prometheus + Grafana监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用监控
  - job_name: 'mes-mdm-app'
    static_configs:
      - targets: ['mes-mdm-app:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
    
  # 数据库监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
      
  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
      
  # Kubernetes监控
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true

---
# alert_rules.yml
groups:
- name: mes-mdm-alerts
  rules:
  # 应用服务告警
  - alert: ServiceDown
    expr: up{job="mes-mdm-app"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "MES-MDM服务不可用"
      description: "服务 {{ $labels.instance }} 已经停止响应超过1分钟"
      
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "错误率过高"
      description: "服务错误率超过5%，当前值: {{ $value }}"
      
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "响应时间过长"
      description: "95%请求响应时间超过2秒，当前值: {{ $value }}s"
      
  # 资源使用告警
  - alert: HighCpuUsage
    expr: (1 - avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m]))) * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CPU使用率过高"
      description: "节点 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"
      
  - alert: HighMemoryUsage
    expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "内存使用率过高"
      description: "节点 {{ $labels.instance }} 内存使用率超过80%，当前值: {{ $value }}%"
```

### 2.2 故障处理流程

#### 2.2.1 故障响应流程
```mermaid
graph TD
    A[告警触发] --> B[故障确认]
    B --> C[影响评估]
    C --> D[启动应急响应]
    D --> E[问题定位]
    E --> F[实施修复]
    F --> G[验证修复]
    G --> H{修复成功?}
    H -->|是| I[恢复服务]
    H -->|否| J[升级处理]
    I --> K[总结分析]
    J --> E
    K --> L[预防措施]
```

#### 2.2.2 故障处理手册
```yaml
常见故障场景:
  
  1. 服务无法启动:
     症状: Pod状态为CrashLoopBackOff
     排查步骤:
       - kubectl describe pod [pod-name]
       - kubectl logs [pod-name] --previous
       - 检查配置文件和环境变量
       - 检查镜像版本和依赖
     解决方案:
       - 修复配置错误
       - 回滚到上一个版本
       - 重启依赖服务
       
  2. 数据库连接异常:
     症状: 应用日志显示数据库连接失败
     排查步骤:
       - 检查数据库服务状态
       - 验证连接参数
       - 检查网络连通性
       - 查看数据库连接数
     解决方案:
       - 重启数据库服务
       - 调整连接池配置
       - 优化数据库性能
       - 清理长时间连接
       
  3. 内存溢出:
     症状: Pod被OOMKilled
     排查步骤:
       - 查看Pod资源限制
       - 分析内存使用趋势
       - 检查内存泄漏
       - 分析堆内存快照
     解决方案:
       - 增加内存限制
       - 优化代码逻辑
       - 调整JVM参数
       - 修复内存泄漏
       
  4. 网络异常:
     症状: 服务间调用超时
     排查步骤:
       - 检查Service和Endpoint
       - 验证网络策略
       - 测试网络连通性
       - 检查DNS解析
     解决方案:
       - 修复网络配置
       - 调整网络策略
       - 重启网络组件
       - 优化网络架构

应急处理预案:
  
  数据库故障:
    主备切换:
      - 评估主库故障程度
      - 确认备库数据一致性
      - 执行主备切换
      - 验证服务恢复
      - 修复故障主库
      
  服务集群故障:
    扩容恢复:
      - 快速扩容健康节点
      - 迁移故障节点负载
      - 隔离故障节点
      - 分析故障原因
      - 修复并重新加入集群
      
  数据损坏:
    备份恢复:
      - 立即停止写入操作
      - 评估数据损坏范围
      - 选择合适备份点
      - 执行数据恢复
      - 验证数据完整性
```

---

## 第三部分：监控运维自动化

### 3.1 自动化运维工具

#### 3.1.1 Kubernetes资源配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mes-mdm-app
  namespace: production
  labels:
    app: mes-mdm
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: mes-mdm
  template:
    metadata:
      labels:
        app: mes-mdm
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      containers:
      - name: mes-mdm-app
        image: registry.company.com/mes-mdm:${IMAGE_TAG}
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: mes-mdm-config
      - name: logs-volume
        emptyDir: {}

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: mes-mdm-service
  namespace: production
  labels:
    app: mes-mdm
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: mes-mdm

---
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: mes-mdm-hpa
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mes-mdm-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

### 3.2 日志管理规范

#### 3.2.1 ELK Stack配置
```yaml
# elasticsearch.yaml
apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: elasticsearch
  namespace: elastic-system
spec:
  version: 8.5.0
  nodeSets:
  - name: default
    count: 3
    config:
      node.store.allow_mmap: false
      xpack.security.enabled: true
      xpack.security.transport.ssl.enabled: true
      xpack.security.http.ssl.enabled: true
    podTemplate:
      spec:
        containers:
        - name: elasticsearch
          resources:
            requests:
              memory: 2Gi
              cpu: 1
            limits:
              memory: 4Gi
              cpu: 2
          env:
          - name: ES_JAVA_OPTS
            value: "-Xms2g -Xmx2g"
    volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 100Gi

---
# kibana.yaml
apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: kibana
  namespace: elastic-system
spec:
  version: 8.5.0
  count: 1
  elasticsearchRef:
    name: elasticsearch
  config:
    server.publicBaseUrl: "https://kibana.company.com"
  http:
    tls:
      selfSignedCertificate:
        disabled: true

---
# filebeat.yaml
apiVersion: beat.k8s.elastic.co/v1beta1
kind: Beat
metadata:
  name: filebeat
  namespace: elastic-system
spec:
  type: filebeat
  version: 8.5.0
  elasticsearchRef:
    name: elasticsearch
  config:
    filebeat.inputs:
    - type: container
      paths:
      - /var/log/containers/*.log
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"
    output.elasticsearch:
      hosts: ["elasticsearch-es-http:9200"]
      username: ${ES_USERNAME}
      password: ${ES_PASSWORD}
      ssl.certificate_authorities: ["/etc/elasticsearch/certs/ca.crt"]
  daemonSet:
    podTemplate:
      spec:
        serviceAccountName: filebeat
        automountServiceAccountToken: true
        containers:
        - name: filebeat
          securityContext:
            runAsUser: 0
          volumeMounts:
          - name: varlogcontainers
            mountPath: /var/log/containers
          - name: varlogpods
            mountPath: /var/log/pods
          - name: varlibdockercontainers
            mountPath: /var/lib/docker/containers
          env:
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
        volumes:
        - name: varlogcontainers
          hostPath:
            path: /var/log/containers
        - name: varlogpods
          hostPath:
            path: /var/log/pods
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
```

---

## 第四部分：安全运维规范

### 4.1 安全防护措施

#### 4.1.1 网络安全配置
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mes-mdm-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: mes-mdm
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app: gateway
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: cache
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: mes-mdm-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
```

#### 4.1.2 访问控制配置
```yaml
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: mes-mdm-service-account
  namespace: production

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: mes-mdm-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: mes-mdm-role-binding
  namespace: production
subjects:
- kind: ServiceAccount
  name: mes-mdm-service-account
  namespace: production
roleRef:
  kind: Role
  name: mes-mdm-role
  apiGroup: rbac.authorization.k8s.io
```

### 4.2 备份恢复策略

#### 4.2.1 数据备份方案
```yaml
备份策略:
  数据库备份:
    全量备份: 每日凌晨2点
    增量备份: 每4小时一次
    备份保留: 30天
    备份验证: 每周一次恢复测试
    
  配置备份:
    Kubernetes配置: 每次变更后备份
    应用配置: 版本控制管理
    证书备份: 每月备份
    
  日志备份:
    应用日志: 保留30天
    审计日志: 保留90天
    监控数据: 保留365天

备份脚本示例:
#!/bin/bash
# PostgreSQL备份脚本

BACKUP_DIR="/backup/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="mes_mdm_db"
DB_USER="backup_user"
DB_HOST="postgresql-primary"

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

# 执行全量备份
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME -F c -f $BACKUP_DIR/$DATE/${DB_NAME}_full_$DATE.dump

# 压缩备份文件
gzip $BACKUP_DIR/$DATE/${DB_NAME}_full_$DATE.dump

# 上传到对象存储
aws s3 cp $BACKUP_DIR/$DATE/${DB_NAME}_full_$DATE.dump.gz s3://backup-bucket/postgresql/$DATE/

# 清理本地文件
find $BACKUP_DIR -type d -mtime +7 -exec rm -rf {} \;

# 备份验证
if [ $? -eq 0 ]; then
    echo "备份成功: ${DB_NAME}_full_$DATE.dump.gz"
else
    echo "备份失败" >&2
    exit 1
fi
```

#### 4.2.2 灾难恢复计划
```yaml
恢复目标:
  RTO (恢复时间目标): 2小时
  RPO (恢复点目标): 15分钟
  数据完整性: 100%
  服务可用性: 99.9%

恢复流程:
  1. 故障评估 (15分钟):
     - 确认故障类型和范围
     - 评估数据损失程度
     - 确定恢复策略
     
  2. 环境准备 (30分钟):
     - 准备恢复环境
     - 验证网络连通性
     - 确认资源可用性
     
  3. 数据恢复 (60分钟):
     - 恢复数据库
     - 恢复应用配置
     - 验证数据完整性
     
  4. 服务恢复 (30分钟):
     - 启动应用服务
     - 执行健康检查
     - 恢复流量路由
     
  5. 验证测试 (15分钟):
     - 功能验证测试
     - 性能验证测试
     - 用户访问验证

恢复测试:
  定期演练: 每季度一次
  测试范围: 全系统恢复
  测试环境: 独立测试环境
  文档更新: 及时更新恢复文档
```

---

## 第五部分：性能优化规范

### 5.1 性能监控和调优

#### 5.1.1 性能基准指标
```yaml
应用性能指标:
  响应时间:
    - API接口: P95 < 1秒, P99 < 2秒
    - 页面加载: P95 < 3秒, P99 < 5秒
    - 数据库查询: P95 < 500ms, P99 < 1秒
    
  吞吐量:
    - API TPS: > 1000
    - 并发用户: > 500
    - 数据处理: > 10000 records/min
    
  资源使用:
    - CPU使用率: < 70%
    - 内存使用率: < 80%
    - 磁盘使用率: < 80%
    - 网络带宽: < 70%

系统性能指标:
  Kubernetes指标:
    - Pod启动时间: < 30秒
    - 服务发现时间: < 5秒
    - 滚动更新时间: < 5分钟
    - 故障恢复时间: < 2分钟
    
  数据库性能:
    - 连接池利用率: < 80%
    - 查询执行时间: < 100ms (简单查询)
    - 锁等待时间: < 10ms
    - 缓存命中率: > 90%
```

#### 5.1.2 性能调优脚本
```bash
#!/bin/bash
# 性能调优脚本

echo "开始性能检查和调优..."

# 1. JVM性能调优
echo "=== JVM性能调优 ==="
kubectl patch deployment mes-mdm-app -p '{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "mes-mdm-app",
          "env": [
            {
              "name": "JAVA_OPTS",
              "value": "-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
            }
          ]
        }]
      }
    }
  }
}'

# 2. 数据库连接池优化
echo "=== 数据库连接池优化 ==="
kubectl create configmap db-config --from-literal=max-pool-size=20 \
  --from-literal=min-pool-size=5 \
  --from-literal=connection-timeout=30000 \
  --from-literal=idle-timeout=600000 \
  --dry-run=client -o yaml | kubectl apply -f -

# 3. Redis缓存优化
echo "=== Redis缓存优化 ==="
kubectl exec redis-master-0 -- redis-cli CONFIG SET maxmemory-policy allkeys-lru
kubectl exec redis-master-0 -- redis-cli CONFIG SET timeout 300

# 4. Nginx负载均衡优化
echo "=== Nginx负载均衡优化 ==="
kubectl patch configmap nginx-config -p '{
  "data": {
    "proxy-connect-timeout": "60s",
    "proxy-send-timeout": "60s",
    "proxy-read-timeout": "60s",
    "proxy-buffer-size": "16k",
    "proxy-buffers": "8 16k"
  }
}'

# 5. 水平扩缩容配置
echo "=== 水平扩缩容配置 ==="
kubectl apply -f - <<EOF
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: mes-mdm-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mes-mdm-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
EOF

echo "性能调优完成!"
```

---

## 附录

### 附录A 运维脚本库
- [服务部署脚本](./scripts/deploy.sh)
- [健康检查脚本](./scripts/health-check.sh)
- [备份恢复脚本](./scripts/backup-restore.sh)
- [性能监控脚本](./scripts/performance-monitor.sh)

### 附录B 配置模板
- [Kubernetes配置模板](./templates/k8s-templates/)
- [监控配置模板](./templates/monitoring-templates/)
- [CI/CD配置模板](./templates/cicd-templates/)

### 附录C 故障处理手册
- [常见故障排查手册](./guides/troubleshooting-guide.md)
- [应急响应流程](./guides/incident-response.md)
- [恢复操作手册](./guides/recovery-procedures.md)

---

**文档信息**:
- **版本**: v1.0
- **创建日期**: 2024年1月15日
- **创建人**: 运维团队
- **审核人**: 技术委员会
- **批准人**: 项目经理
- **生效日期**: 2024年2月1日

---

*本规范为项目部署运维的标准指南，确保系统稳定高效运行。所有运维人员都应严格遵循本规范执行运维操作。*
